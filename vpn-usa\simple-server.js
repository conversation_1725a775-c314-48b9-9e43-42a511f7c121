const http = require('http');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { URL } = require('url');

// إعدادات الخادم
const PORT = process.env.PORT || 3001;
const VPN_KEY = process.env.VPN_KEY || crypto.randomBytes(32).toString('hex');

// خوادم أمريكية وهمية
const USA_SERVERS = [
  { id: 'us-east-1', name: 'New York', ip: '*************', port: 8080, load: 25 },
  { id: 'us-west-1', name: 'Los Angeles', ip: '*************', port: 8080, load: 40 },
  { id: 'us-central-1', name: 'Chicago', ip: '*************', port: 8080, load: 15 },
  { id: 'us-south-1', name: 'Miami', ip: '*************', port: 8080, load: 30 }
];

// إدارة العملاء
const clients = new Map();
let clientCounter = 0;

// دالة للحصول على نوع المحتوى
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const types = {
    '.html': 'text/html; charset=utf-8',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.ico': 'image/x-icon'
  };
  return types[ext] || 'text/plain';
}

// دالة لقراءة الملفات
function serveFile(res, filePath) {
  const fullPath = path.join(__dirname, 'client', filePath);
  
  fs.readFile(fullPath, (err, data) => {
    if (err) {
      res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end('<h1>404 - الملف غير موجود</h1>');
      return;
    }
    
    res.writeHead(200, { 
      'Content-Type': getContentType(fullPath),
      'Access-Control-Allow-Origin': '*'
    });
    res.end(data);
  });
}

// إنشاء الخادم
const server = http.createServer((req, res) => {
  const url = new URL(req.url, `http://localhost:${PORT}`);
  const pathname = url.pathname;

  console.log(`📡 ${req.method} ${pathname}`);

  // إعداد CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // الصفحة الرئيسية
  if (pathname === '/' || pathname === '/index.html') {
    serveFile(res, 'index.html');
    return;
  }

  // الملفات الثابتة
  if (pathname.startsWith('/client/') || pathname.endsWith('.css') || pathname.endsWith('.js')) {
    const filePath = pathname.startsWith('/client/') ? pathname.substring(8) : pathname.substring(1);
    serveFile(res, filePath);
    return;
  }

  // API للخوادم
  if (pathname === '/api/servers') {
    const serversWithStatus = USA_SERVERS.map(server => ({
      ...server,
      status: 'online',
      ping: Math.floor(Math.random() * 50) + 10
    }));

    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
    res.end(JSON.stringify({
      success: true,
      servers: serversWithStatus
    }));
    return;
  }

  // API للحالة
  if (pathname === '/api/status') {
    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
    res.end(JSON.stringify({
      success: true,
      connectedClients: clients.size,
      activeServers: USA_SERVERS.length,
      serverLoad: Math.floor(Math.random() * 60) + 20,
      uptime: process.uptime()
    }));
    return;
  }

  // API للـ IP
  if (pathname === '/api/ip') {
    const clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress;
    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
    res.end(JSON.stringify({
      success: true,
      ip: clientIp,
      country: 'Unknown',
      city: 'Unknown'
    }));
    return;
  }

  // API للبروكسي
  if (pathname === '/api/proxy' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', async () => {
      try {
        const { url: targetUrl, method = 'GET' } = JSON.parse(body);
        
        // محاكاة طلب البروكسي
        const https = require('https');
        const http = require('http');
        
        const urlObj = new URL(targetUrl);
        const isHttps = urlObj.protocol === 'https:';
        const client = isHttps ? https : http;

        const options = {
          hostname: urlObj.hostname,
          port: urlObj.port || (isHttps ? 443 : 80),
          path: urlObj.pathname + urlObj.search,
          method: method,
          headers: {
            'User-Agent': 'VPN-USA-Client/1.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
          }
        };

        const proxyReq = client.request(options, (proxyRes) => {
          let responseData = '';
          
          proxyRes.on('data', chunk => {
            responseData += chunk;
          });

          proxyRes.on('end', () => {
            res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({
              success: true,
              data: {
                status: proxyRes.statusCode,
                statusText: proxyRes.statusMessage,
                headers: proxyRes.headers,
                data: responseData,
                url: targetUrl,
                method: method,
                timestamp: new Date().toISOString()
              }
            }));
          });
        });

        proxyReq.on('error', (error) => {
          res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({
            success: false,
            error: error.message
          }));
        });

        proxyReq.setTimeout(30000, () => {
          proxyReq.destroy();
          res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({
            success: false,
            error: 'Request timeout'
          }));
        });

        proxyReq.end();

      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
          success: false,
          error: 'Invalid request data'
        }));
      }
    });
    return;
  }

  // 404 للطلبات غير الموجودة
  res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
  res.end(`
    <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>404 - غير موجود</title>
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
          h1 { color: #f44336; }
        </style>
      </head>
      <body>
        <h1>404 - الصفحة غير موجودة</h1>
        <p>الصفحة التي تبحث عنها غير موجودة.</p>
        <a href="/">العودة للصفحة الرئيسية</a>
      </body>
    </html>
  `);
});

// بدء الخادم
server.listen(PORT, () => {
  console.log('🇺🇸 VPN USA Server Starting...');
  console.log('🔐 VPN Key:', VPN_KEY.substring(0, 8) + '...');
  console.log(`🚀 VPN USA Server running on port ${PORT}`);
  console.log(`🌐 Access dashboard: http://localhost:${PORT}`);
  console.log(`🇺🇸 Available USA servers: ${USA_SERVERS.length}`);
});

// معالجة الأخطاء
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});
