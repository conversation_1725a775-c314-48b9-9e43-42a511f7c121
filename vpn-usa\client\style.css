/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600;700&display=swap');

body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', sans-serif;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    min-height: 100vh;
    color: #fff;
    direction: rtl;
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.1), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.05), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.1), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.05), transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: stars 20s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes stars {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-200px); }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    z-index: 2;
}

.logo i {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #00f5ff, #ff00ff, #00ff00);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: rainbow 2s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(0, 245, 255, 0.5));
}

.logo h1 {
    font-family: 'Orbitron', monospace;
    color: #fff;
    font-size: 2rem;
    font-weight: 900;
    text-shadow:
        0 0 10px rgba(0, 245, 255, 0.5),
        0 0 20px rgba(0, 245, 255, 0.3),
        0 0 30px rgba(0, 245, 255, 0.1);
    letter-spacing: 2px;
}

@keyframes rainbow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 25px;
    background: linear-gradient(135deg,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(255, 255, 255, 0.1) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 30px;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
}

.status-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    position: relative;
    animation: pulse 2s infinite;
}

.status-dot::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    animation: ripple 2s infinite;
}

.status-dot.online {
    background: #00ff88;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
}

.status-dot.online::before {
    border: 2px solid rgba(0, 255, 136, 0.3);
}

.status-dot.offline {
    background: #ff4757;
    box-shadow: 0 0 20px rgba(255, 71, 87, 0.6);
}

.status-dot.offline::before {
    border: 2px solid rgba(255, 71, 87, 0.3);
}

.status-dot.connecting {
    background: #ffa502;
    box-shadow: 0 0 20px rgba(255, 165, 2, 0.6);
}

.status-dot.connecting::before {
    border: 2px solid rgba(255, 165, 2, 0.3);
}

.status-text {
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

@keyframes ripple {
    0% { transform: scale(0.8); opacity: 1; }
    100% { transform: scale(2); opacity: 0; }
}

/* Dashboard */
.dashboard {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.dashboard section {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 30px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.dashboard section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.05) 50%,
        transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.dashboard section:hover::before {
    transform: translateX(100%);
}

.dashboard section:hover {
    transform: translateY(-5px);
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 30px rgba(0, 245, 255, 0.2);
}

.dashboard h2 {
    color: #fff;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.4rem;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 2;
}

.dashboard h2 i {
    background: linear-gradient(45deg, #00f5ff, #ff00ff);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: rainbow 3s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(0, 245, 255, 0.5));
    font-size: 1.6rem;
}

/* Connection Panel */
.connection-panel {
    grid-column: 1 / -1;
}

.current-status {
    margin-bottom: 25px;
}

.status-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.ip-info p {
    margin: 5px 0;
    font-size: 1.1rem;
}

.server-selection {
    margin-bottom: 25px;
}

.servers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.server-card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.4s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.server-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        transparent,
        rgba(0, 245, 255, 0.1),
        transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.server-card:hover::before {
    transform: translateX(100%);
}

.server-card:hover {
    border-color: #00f5ff;
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 245, 255, 0.4);
}

.server-card.selected {
    border-color: #00ff88;
    background: linear-gradient(135deg,
        rgba(0, 255, 136, 0.2) 0%,
        rgba(0, 255, 136, 0.1) 100%);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.3),
        0 0 25px rgba(0, 255, 136, 0.5);
    animation: selectedPulse 2s infinite;
}

@keyframes selectedPulse {
    0% { box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 25px rgba(0, 255, 136, 0.5); }
    50% { box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 35px rgba(0, 255, 136, 0.7); }
    100% { box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 25px rgba(0, 255, 136, 0.5); }
}

.server-card h4 {
    color: #fff;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    font-size: 1.1rem;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 2;
}

.server-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #666;
}

.load-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
}

.load-bar {
    width: 50px;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
}

.load-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #ff9800, #f44336);
    transition: width 0.3s ease;
}

.connection-controls {
    text-align: center;
}

/* Buttons */
.btn {
    padding: 15px 35px;
    border: 2px solid transparent;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
    cursor: pointer;
    transition: all 0.4s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #00f5ff 0%, #ff00ff 50%, #00ff88 100%);
    background-size: 200% 200%;
    color: #000;
    border-color: rgba(255, 255, 255, 0.3);
    animation: gradientShift 3s ease infinite;
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-5px) scale(1.05);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 245, 255, 0.6);
    border-color: #00f5ff;
}

.btn-danger {
    background: linear-gradient(135deg, #ff4757 0%, #ff3838 50%, #ff6b7a 100%);
    background-size: 200% 200%;
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    animation: gradientShift 3s ease infinite;
    box-shadow: 0 0 20px rgba(255, 71, 87, 0.3);
}

.btn-danger:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(255, 71, 87, 0.6);
    border-color: #ff4757;
}

.btn-secondary {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 15px rgba(116, 185, 255, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.3),
        0 0 25px rgba(116, 185, 255, 0.5);
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Statistics Panel */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    color: white;
    padding: 25px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(0, 245, 255, 0.1) 50%,
        transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.stat-card:hover::before {
    transform: translateX(100%);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 245, 255, 0.3);
}

.stat-icon {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #00f5ff, #ff00ff, #00ff88);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: rainbow 3s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(0, 245, 255, 0.5));
    position: relative;
    z-index: 2;
}

.stat-info {
    position: relative;
    z-index: 2;
}

.stat-info h3 {
    font-size: 2rem;
    margin-bottom: 8px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    animation: numberGlow 2s ease-in-out infinite alternate;
}

.stat-info p {
    opacity: 0.9;
    font-size: 1rem;
    font-weight: 500;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.2);
}

@keyframes numberGlow {
    0% { text-shadow: 0 0 10px rgba(255, 255, 255, 0.3); }
    100% { text-shadow: 0 0 20px rgba(0, 245, 255, 0.6), 0 0 30px rgba(0, 245, 255, 0.3); }
}

/* Browser Panel */
.browser-panel {
    grid-column: 1 / -1;
}

.browser-controls {
    margin-bottom: 20px;
}

.url-bar {
    display: flex;
    gap: 10px;
}

.url-bar input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;
    direction: ltr;
    text-align: left;
}

.url-bar input:focus {
    border-color: #667eea;
}

.browser-content {
    background: #f8f9fa;
    border-radius: 10px;
    min-height: 400px;
    overflow: hidden;
    position: relative;
}

.browser-frame {
    width: 100%;
    height: 400px;
    border: none;
    background: white;
    padding: 20px;
    overflow-y: auto;
}

.welcome-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: #666;
}

.welcome-message i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.response-content {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    border-left: 4px solid #667eea;
}

.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.response-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.response-status.success {
    background: #d4edda;
    color: #155724;
}

.response-status.error {
    background: #f8d7da;
    color: #721c24;
}

.response-body {
    max-height: 300px;
    overflow-y: auto;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    direction: ltr;
    text-align: left;
}

.response-info {
    background: #e3f2fd;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    border-left: 4px solid #2196F3;
}

.response-info p {
    margin: 5px 0;
    font-size: 0.9rem;
}

.response-info strong {
    color: #1976D2;
}

/* Notifications */
.notifications {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    max-width: 400px;
}

.notification {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px 25px;
    margin-bottom: 15px;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-left: 4px solid #00f5ff;
    animation: slideIn 0.5s ease, notificationGlow 2s ease-in-out infinite alternate;
    color: #fff;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    animation: notificationShine 3s infinite;
}

.notification.success {
    border-left-color: #00ff88;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(0, 255, 136, 0.3);
}

.notification.error {
    border-left-color: #ff4757;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(255, 71, 87, 0.3);
}

.notification.warning {
    border-left-color: #ffa502;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(255, 165, 2, 0.3);
}

@keyframes notificationGlow {
    0% { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 245, 255, 0.3); }
    100% { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 25px rgba(0, 245, 255, 0.5); }
}

@keyframes notificationShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Footer */
.footer {
    text-align: center;
    padding: 30px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    font-family: 'Orbitron', monospace;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.02) 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 245, 255, 0.5),
        transparent);
    animation: footerGlow 3s ease-in-out infinite;
}

@keyframes footerGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* تأثيرات إضافية للعناصر التفاعلية */
.server-info {
    color: rgba(255, 255, 255, 0.8);
    position: relative;
    z-index: 2;
}

.load-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.load-bar {
    width: 60px;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.load-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ff88, #00f5ff, #ff00ff);
    background-size: 200% 100%;
    animation: loadAnimation 2s ease-in-out infinite;
    transition: width 0.3s ease;
    border-radius: 4px;
}

@keyframes loadAnimation {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard {
        grid-template-columns: 1fr;
    }

    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .servers-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .url-bar {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }
}
