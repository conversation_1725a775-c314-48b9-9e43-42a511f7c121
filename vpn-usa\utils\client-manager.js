class ClientManager {
  constructor() {
    this.clients = new Map();
    this.serverConnections = new Map();
  }

  // إضافة عميل جديد
  addClient(clientId, socketId, ipAddress) {
    const client = {
      id: clientId,
      socketId: socketId,
      ipAddress: ipAddress,
      connectedAt: new Date(),
      connectedServer: null,
      dataTransferred: 0,
      status: 'connected'
    };

    this.clients.set(clientId, client);
    console.log(`👤 عميل جديد مسجل: ${clientId} من ${ipAddress}`);
    return client;
  }

  // الحصول على عميل
  getClient(clientId) {
    return this.clients.get(clientId);
  }

  // تحديث خادم العميل
  updateClientServer(clientId, serverId) {
    const client = this.clients.get(clientId);
    if (client) {
      // إزالة من الخادم السابق
      if (client.connectedServer) {
        this.removeFromServer(client.connectedServer, clientId);
      }

      // إضافة للخادم الجديد
      if (serverId) {
        client.connectedServer = serverId;
        this.addToServer(serverId, clientId);
      } else {
        client.connectedServer = null;
      }

      this.clients.set(clientId, client);
    }
  }

  // إضافة عميل لخادم
  addToServer(serverId, clientId) {
    if (!this.serverConnections.has(serverId)) {
      this.serverConnections.set(serverId, new Set());
    }
    this.serverConnections.get(serverId).add(clientId);
  }

  // إزالة عميل من خادم
  removeFromServer(serverId, clientId) {
    if (this.serverConnections.has(serverId)) {
      this.serverConnections.get(serverId).delete(clientId);
      if (this.serverConnections.get(serverId).size === 0) {
        this.serverConnections.delete(serverId);
      }
    }
  }

  // إزالة عميل
  removeClient(clientId) {
    const client = this.clients.get(clientId);
    if (client && client.connectedServer) {
      this.removeFromServer(client.connectedServer, clientId);
    }
    this.clients.delete(clientId);
    console.log(`👤 عميل محذوف: ${clientId}`);
  }

  // الحصول على عدد العملاء
  getClientCount() {
    return this.clients.size;
  }

  // الحصول على عملاء خادم معين
  getServerClients(serverId) {
    return this.serverConnections.get(serverId) || new Set();
  }

  // الحصول على إحصائيات
  getStats() {
    const stats = {
      totalClients: this.clients.size,
      serversInUse: this.serverConnections.size,
      clientsPerServer: {}
    };

    for (const [serverId, clients] of this.serverConnections) {
      stats.clientsPerServer[serverId] = clients.size;
    }

    return stats;
  }

  // تحديث البيانات المنقولة
  updateDataTransferred(clientId, bytes) {
    const client = this.clients.get(clientId);
    if (client) {
      client.dataTransferred += bytes;
      this.clients.set(clientId, client);
    }
  }

  // الحصول على جميع العملاء
  getAllClients() {
    return Array.from(this.clients.values());
  }

  // تنظيف العملاء المنقطعين
  cleanup() {
    const now = new Date();
    const timeout = 5 * 60 * 1000; // 5 دقائق

    for (const [clientId, client] of this.clients) {
      if (now - client.connectedAt > timeout && client.status === 'disconnected') {
        this.removeClient(clientId);
      }
    }
  }
}

module.exports = ClientManager;
