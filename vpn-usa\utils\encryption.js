const crypto = require('crypto');

class Encryption {
  constructor(key) {
    this.algorithm = 'aes-256-gcm';
    this.key = Buffer.from(key, 'hex');
    if (this.key.length !== 32) {
      this.key = crypto.scryptSync(key, 'salt', 32);
    }
  }

  // تشفير البيانات
  encrypt(text) {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(this.algorithm, this.key);
      cipher.setAAD(Buffer.from('vpn-usa-auth', 'utf8'));
      
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return {
        encrypted: encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('خطأ في التشفير:', error);
      throw new Error('فشل في تشفير البيانات');
    }
  }

  // فك التشفير
  decrypt(encryptedData) {
    try {
      const { encrypted, iv, authTag, timestamp } = encryptedData;
      
      // التحقق من صحة الوقت (البيانات صالحة لمدة ساعة)
      if (Date.now() - timestamp > 3600000) {
        throw new Error('البيانات المشفرة منتهية الصلاحية');
      }

      const decipher = crypto.createDecipher(this.algorithm, this.key);
      decipher.setAAD(Buffer.from('vpn-usa-auth', 'utf8'));
      decipher.setAuthTag(Buffer.from(authTag, 'hex'));

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      console.error('خطأ في فك التشفير:', error);
      throw new Error('فشل في فك تشفير البيانات');
    }
  }

  // إنشاء hash للبيانات
  createHash(data) {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  // التحقق من hash
  verifyHash(data, hash) {
    const computedHash = this.createHash(data);
    return computedHash === hash;
  }

  // إنشاء توقيع رقمي
  sign(data) {
    const hmac = crypto.createHmac('sha256', this.key);
    hmac.update(data);
    return hmac.digest('hex');
  }

  // التحقق من التوقيع
  verifySignature(data, signature) {
    const computedSignature = this.sign(data);
    return computedSignature === signature;
  }

  // إنشاء مفتاح جلسة عشوائي
  generateSessionKey() {
    return crypto.randomBytes(32).toString('hex');
  }

  // تشفير كلمة المرور
  hashPassword(password, salt = null) {
    if (!salt) {
      salt = crypto.randomBytes(16).toString('hex');
    }
    const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
    return { hash, salt };
  }

  // التحقق من كلمة المرور
  verifyPassword(password, hash, salt) {
    const computedHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
    return computedHash === hash;
  }

  // تشفير URL
  encryptUrl(url) {
    const encrypted = this.encrypt(url);
    return Buffer.from(JSON.stringify(encrypted)).toString('base64');
  }

  // فك تشفير URL
  decryptUrl(encryptedUrl) {
    try {
      const encryptedData = JSON.parse(Buffer.from(encryptedUrl, 'base64').toString());
      return this.decrypt(encryptedData);
    } catch (error) {
      throw new Error('URL مشفر غير صحيح');
    }
  }

  // إنشاء رمز مصادقة
  generateAuthToken(clientId) {
    const payload = {
      clientId: clientId,
      timestamp: Date.now(),
      random: crypto.randomBytes(16).toString('hex')
    };
    
    const token = Buffer.from(JSON.stringify(payload)).toString('base64');
    const signature = this.sign(token);
    
    return `${token}.${signature}`;
  }

  // التحقق من رمز المصادقة
  verifyAuthToken(token) {
    try {
      const [payload, signature] = token.split('.');
      
      if (!this.verifySignature(payload, signature)) {
        return null;
      }

      const data = JSON.parse(Buffer.from(payload, 'base64').toString());
      
      // التحقق من انتهاء الصلاحية (24 ساعة)
      if (Date.now() - data.timestamp > 86400000) {
        return null;
      }

      return data;
    } catch (error) {
      return null;
    }
  }
}

module.exports = Encryption;
