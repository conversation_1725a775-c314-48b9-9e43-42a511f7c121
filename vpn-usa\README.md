# 🇺🇸 VPN USA - الشبكة الخاصة الآمنة

VPN خاص وآمن مع خوادم أمريكية متعددة للحصول على أفضل أداء وحماية.

## ✨ المميزات

- 🔐 **تشفير AES-256**: حماية قصوى لبياناتك
- 🇺🇸 **خوادم أمريكية متعددة**: New York, Los Angeles, Chicago, Miami
- 🌐 **متصفح آمن مدمج**: تصفح مجهول وآمن
- 📊 **لوحة تحكم متقدمة**: مراقبة الاتصالات والإحصائيات
- ⚡ **أداء عالي**: اتصال سريع ومستقر
- 🛡️ **حماية من التتبع**: إخفاء هويتك على الإنترنت

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js 16.0.0 أو أحدث
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd vpn-usa
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل الخادم**
```bash
npm start
```

4. **للتطوير (مع إعادة التشغيل التلقائي)**
```bash
npm run dev
```

5. **فتح المتصفح**
```
http://localhost:3001
```

## 🔧 الإعدادات

### متغيرات البيئة
قم بإنشاء ملف `.env` في المجلد الجذر:

```env
PORT=3001
VPN_KEY=your-secret-encryption-key-here
NODE_ENV=production
```

### إعدادات الخوادم
يمكنك تعديل قائمة الخوادم الأمريكية في ملف `server.js`:

```javascript
const USA_SERVERS = [
  { id: 'us-east-1', name: 'New York', ip: '*************', port: 8080, load: 25 },
  { id: 'us-west-1', name: 'Los Angeles', ip: '*************', port: 8080, load: 40 },
  // إضافة المزيد من الخوادم...
];
```

## 📱 كيفية الاستخدام

### 1. الاتصال بخادم أمريكي
- اختر خادم من القائمة المتاحة
- انقر على "اتصال"
- انتظر حتى يتم تأكيد الاتصال

### 2. التصفح الآمن
- أدخل الرابط في شريط العناوين
- انقر على زر "انتقال"
- تمتع بالتصفح الآمن عبر الخادم الأمريكي

### 3. مراقبة الإحصائيات
- عدد العملاء المتصلين
- حالة الخوادم
- حمولة الشبكة
- وقت التشغيل

## 🏗️ بنية المشروع

```
vpn-usa/
├── server.js              # الخادم الرئيسي
├── package.json           # تبعيات المشروع
├── utils/                 # أدوات مساعدة
│   ├── client-manager.js  # إدارة العملاء
│   ├── encryption.js      # نظام التشفير
│   └── proxy-handler.js   # معالج البروكسي
├── client/                # واجهة المستخدم
│   ├── index.html         # الصفحة الرئيسية
│   ├── style.css          # التصميم
│   └── client.js          # منطق العميل
└── README.md              # هذا الملف
```

## 🔒 الأمان

### التشفير
- **AES-256-GCM**: تشفير البيانات
- **HMAC-SHA256**: التوقيع الرقمي
- **PBKDF2**: تشفير كلمات المرور
- **Random IV**: لكل عملية تشفير

### الحماية
- تصفية النطاقات المحظورة
- التحقق من صحة الطلبات
- حماية من هجمات CSRF
- تنظيف headers الحساسة

## 📊 API المتاح

### GET /api/servers
الحصول على قائمة الخوادم الأمريكية المتاحة

### GET /api/status
الحصول على إحصائيات الخادم

### GET /api/ip
الحصول على عنوان IP الحالي

### WebSocket Events
- `connect-to-server`: الاتصال بخادم
- `disconnect-server`: قطع الاتصال
- `proxy-request`: طلب بروكسي

## 🧪 الاختبار

```bash
# تشغيل الاختبارات
npm test

# اختبار الاتصال
node test/test-connection.js
```

## 🚨 تحذيرات مهمة

⚠️ **هذا VPN تعليمي وليس للاستخدام في بيئات الإنتاج الحقيقية**

للاستخدام الفعلي، ننصح بـ:
- OpenVPN
- WireGuard
- NordVPN
- ExpressVPN

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للـ branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك أسئلة:

- فتح Issue في GitHub
- مراسلة المطور
- مراجعة الوثائق

## 🔄 التحديثات

### الإصدار 1.0.0
- إطلاق أولي
- 4 خوادم أمريكية
- متصفح آمن مدمج
- لوحة تحكم متقدمة
- تشفير AES-256

---

**صنع بـ ❤️ للمجتمع العربي**
