const axios = require('axios');
const { URL } = require('url');

class ProxyHandler {
  constructor() {
    this.blockedDomains = new Set([
      'malware.com',
      'phishing.com',
      'spam.com'
    ]);
    
    this.allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
    this.requestCount = 0;
    this.requestHistory = [];
  }

  // معالجة طلب البروكسي
  async handleRequest(url, method = 'GET', headers = {}, data = null) {
    try {
      // التحقق من صحة URL
      if (!this.isValidUrl(url)) {
        throw new Error('URL غير صحيح');
      }

      // التحقق من الطريقة المسموحة
      if (!this.allowedMethods.includes(method.toUpperCase())) {
        throw new Error('طريقة HTTP غير مسموحة');
      }

      // التحقق من النطاقات المحظورة
      if (this.isBlockedDomain(url)) {
        throw new Error('هذا النطاق محظور');
      }

      // إعداد headers الافتراضية
      const proxyHeaders = {
        'User-Agent': 'VPN-USA-Client/1.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        ...headers
      };

      // إزالة headers حساسة
      delete proxyHeaders['host'];
      delete proxyHeaders['origin'];
      delete proxyHeaders['referer'];

      // إعداد الطلب
      const requestConfig = {
        method: method.toUpperCase(),
        url: url,
        headers: proxyHeaders,
        timeout: 30000, // 30 ثانية
        maxRedirects: 5,
        validateStatus: () => true // قبول جميع status codes
      };

      // إضافة البيانات للطلبات POST/PUT
      if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
        requestConfig.data = data;
      }

      console.log(`🌐 Proxy request: ${method} ${url}`);

      // تنفيذ الطلب
      const response = await axios(requestConfig);

      // تسجيل الطلب
      this.logRequest(url, method, response.status);

      // إعداد الاستجابة
      const proxyResponse = {
        status: response.status,
        statusText: response.statusText,
        headers: this.filterResponseHeaders(response.headers),
        data: response.data,
        url: url,
        method: method,
        timestamp: new Date().toISOString(),
        size: JSON.stringify(response.data).length
      };

      return proxyResponse;

    } catch (error) {
      console.error('❌ Proxy error:', error.message);
      
      // تسجيل الخطأ
      this.logRequest(url, method, 'ERROR');

      return {
        status: 500,
        statusText: 'Proxy Error',
        headers: {},
        data: {
          error: true,
          message: error.message,
          type: 'proxy_error'
        },
        url: url,
        method: method,
        timestamp: new Date().toISOString()
      };
    }
  }

  // التحقق من صحة URL
  isValidUrl(urlString) {
    try {
      const url = new URL(urlString);
      return ['http:', 'https:'].includes(url.protocol);
    } catch (error) {
      return false;
    }
  }

  // التحقق من النطاقات المحظورة
  isBlockedDomain(urlString) {
    try {
      const url = new URL(urlString);
      const hostname = url.hostname.toLowerCase();
      
      for (const blockedDomain of this.blockedDomains) {
        if (hostname.includes(blockedDomain)) {
          return true;
        }
      }
      
      return false;
    } catch (error) {
      return true; // إذا لم نتمكن من تحليل URL، نعتبره محظور
    }
  }

  // تصفية headers الاستجابة
  filterResponseHeaders(headers) {
    const filteredHeaders = { ...headers };
    
    // إزالة headers حساسة
    delete filteredHeaders['set-cookie'];
    delete filteredHeaders['server'];
    delete filteredHeaders['x-powered-by'];
    delete filteredHeaders['x-frame-options'];
    
    return filteredHeaders;
  }

  // تسجيل الطلبات
  logRequest(url, method, status) {
    this.requestCount++;
    
    const logEntry = {
      id: this.requestCount,
      url: url,
      method: method,
      status: status,
      timestamp: new Date(),
      userAgent: 'VPN-USA-Client'
    };

    this.requestHistory.push(logEntry);

    // الاحتفاظ بآخر 1000 طلب فقط
    if (this.requestHistory.length > 1000) {
      this.requestHistory = this.requestHistory.slice(-1000);
    }

    console.log(`📊 Request logged: ${method} ${url} - ${status}`);
  }

  // الحصول على إحصائيات الطلبات
  getStats() {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    const recentRequests = this.requestHistory.filter(
      req => req.timestamp > oneHourAgo
    );

    return {
      totalRequests: this.requestCount,
      recentRequests: recentRequests.length,
      successfulRequests: recentRequests.filter(req => 
        typeof req.status === 'number' && req.status < 400
      ).length,
      errorRequests: recentRequests.filter(req => 
        req.status === 'ERROR' || (typeof req.status === 'number' && req.status >= 400)
      ).length,
      topDomains: this.getTopDomains(recentRequests)
    };
  }

  // الحصول على أكثر النطاقات استخداماً
  getTopDomains(requests) {
    const domainCount = {};
    
    requests.forEach(req => {
      try {
        const url = new URL(req.url);
        const domain = url.hostname;
        domainCount[domain] = (domainCount[domain] || 0) + 1;
      } catch (error) {
        // تجاهل URLs غير صحيحة
      }
    });

    return Object.entries(domainCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([domain, count]) => ({ domain, count }));
  }

  // إضافة نطاق محظور
  blockDomain(domain) {
    this.blockedDomains.add(domain.toLowerCase());
    console.log(`🚫 Domain blocked: ${domain}`);
  }

  // إزالة نطاق من القائمة المحظورة
  unblockDomain(domain) {
    this.blockedDomains.delete(domain.toLowerCase());
    console.log(`✅ Domain unblocked: ${domain}`);
  }

  // الحصول على قائمة النطاقات المحظورة
  getBlockedDomains() {
    return Array.from(this.blockedDomains);
  }
}

module.exports = ProxyHandler;
