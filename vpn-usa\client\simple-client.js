// VPN USA Simple Client JavaScript
class SimpleVPNClient {
    constructor() {
        this.isConnected = false;
        this.selectedServer = null;
        this.connectedServer = null;
        this.clientId = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadServers();
        this.loadStats();
        this.getCurrentIP();
        this.simulateConnection();
        
        // تحديث الإحصائيات كل 30 ثانية
        setInterval(() => {
            this.loadStats();
        }, 30000);
    }

    simulateConnection() {
        // محاكاة الاتصال بالخادم
        setTimeout(() => {
            this.isConnected = true;
            this.clientId = 'client-' + Math.random().toString(36).substr(2, 9);
            this.updateConnectionStatus('online', 'متصل');
            this.showNotification('تم الاتصال بخادم VPN بنجاح', 'success');
        }, 1000);
    }

    setupEventListeners() {
        // أزرار الاتصال
        document.getElementById('connectBtn').addEventListener('click', () => {
            this.connectToVPNServer();
        });

        document.getElementById('disconnectBtn').addEventListener('click', () => {
            this.disconnectFromVPNServer();
        });

        // متصفح آمن
        document.getElementById('goBtn').addEventListener('click', () => {
            this.navigateToURL();
        });

        document.getElementById('urlInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.navigateToURL();
            }
        });
    }

    async loadServers() {
        try {
            const response = await fetch('/api/servers');
            const data = await response.json();
            
            if (data.success) {
                this.displayServers(data.servers);
            }
        } catch (error) {
            console.error('خطأ في تحميل الخوادم:', error);
            this.showNotification('خطأ في تحميل الخوادم', 'error');
        }
    }

    async loadStats() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            if (data.success) {
                this.updateStats(data);
            }
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
        }
    }

    async getCurrentIP() {
        try {
            const response = await fetch('/api/ip');
            const data = await response.json();
            
            if (data.success) {
                this.updateCurrentIP(data.ip, data.country);
            }
        } catch (error) {
            console.error('خطأ في الحصول على IP:', error);
        }
    }

    displayServers(servers) {
        const serversGrid = document.getElementById('serversGrid');
        serversGrid.innerHTML = '';

        servers.forEach(server => {
            const serverCard = document.createElement('div');
            serverCard.className = 'server-card';
            serverCard.dataset.serverId = server.id;

            serverCard.innerHTML = `
                <h4>🇺🇸 ${server.name}</h4>
                <div class="server-info">
                    <span>Ping: ${server.ping}ms</span>
                    <div class="load-indicator">
                        <span>${server.load}%</span>
                        <div class="load-bar">
                            <div class="load-fill" style="width: ${server.load}%"></div>
                        </div>
                    </div>
                </div>
            `;

            serverCard.addEventListener('click', () => {
                this.selectServer(server, serverCard);
            });

            serversGrid.appendChild(serverCard);
        });
    }

    selectServer(server, cardElement) {
        // إزالة التحديد السابق
        document.querySelectorAll('.server-card').forEach(card => {
            card.classList.remove('selected');
        });

        // تحديد الخادم الجديد
        cardElement.classList.add('selected');
        this.selectedServer = server;
        
        // تفعيل زر الاتصال
        document.getElementById('connectBtn').disabled = false;
        
        console.log('تم اختيار خادم:', server.name);
        this.showNotification(`تم اختيار خادم ${server.name}`, 'info');
    }

    connectToVPNServer() {
        if (!this.selectedServer) {
            this.showNotification('يرجى اختيار خادم أولاً', 'warning');
            return;
        }

        if (!this.isConnected) {
            this.showNotification('غير متصل مع خادم VPN', 'error');
            return;
        }

        console.log('جاري الاتصال مع:', this.selectedServer.name);
        this.showNotification(`جاري الاتصال مع ${this.selectedServer.name}...`, 'info');

        // محاكاة الاتصال
        setTimeout(() => {
            this.connectedServer = this.selectedServer;
            this.updateServerConnection(true);
            this.showNotification(`متصل مع خادم ${this.selectedServer.name} بنجاح`, 'success');
            this.updateCurrentIP(this.selectedServer.ip, this.selectedServer.name);
        }, 2000);
    }

    disconnectFromVPNServer() {
        if (!this.connectedServer) {
            this.showNotification('غير متصل مع أي خادم', 'warning');
            return;
        }

        console.log('جاري قطع الاتصال...');
        this.showNotification('جاري قطع الاتصال...', 'info');

        // محاكاة قطع الاتصال
        setTimeout(() => {
            this.connectedServer = null;
            this.updateServerConnection(false);
            this.showNotification('تم قطع الاتصال مع الخادم', 'warning');
            this.getCurrentIP();
        }, 1000);
    }

    updateServerConnection(connected) {
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');

        if (connected) {
            connectBtn.style.display = 'none';
            disconnectBtn.style.display = 'inline-flex';
        } else {
            connectBtn.style.display = 'inline-flex';
            disconnectBtn.style.display = 'none';
            connectBtn.disabled = !this.selectedServer;
        }
    }

    updateConnectionStatus(status, text) {
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.querySelector('.status-text');

        statusDot.className = `status-dot ${status}`;
        statusText.textContent = text;
    }

    updateCurrentIP(ip, location) {
        document.getElementById('currentIp').textContent = ip;
        document.getElementById('currentLocation').textContent = location || 'غير معروف';
    }

    updateStats(stats) {
        document.getElementById('connectedClients').textContent = stats.connectedClients;
        document.getElementById('activeServers').textContent = stats.activeServers;
        document.getElementById('serverLoad').textContent = `${stats.serverLoad}%`;
        
        // تحويل وقت التشغيل
        const uptime = Math.floor(stats.uptime);
        const hours = Math.floor(uptime / 3600);
        const minutes = Math.floor((uptime % 3600) / 60);
        document.getElementById('uptime').textContent = `${hours}:${minutes.toString().padStart(2, '0')}`;
    }

    async navigateToURL() {
        const urlInput = document.getElementById('urlInput');
        const url = urlInput.value.trim();

        if (!url) {
            this.showNotification('يرجى إدخال رابط صحيح', 'warning');
            return;
        }

        if (!this.connectedServer) {
            this.showNotification('يرجى الاتصال بخادم أمريكي أولاً', 'warning');
            return;
        }

        // إضافة http:// إذا لم يكن موجود
        let fullUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            fullUrl = 'https://' + url;
        }

        console.log('جاري تحميل:', fullUrl);
        this.showNotification(`جاري تحميل ${fullUrl}...`, 'info');

        try {
            const response = await fetch('/api/proxy', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    url: fullUrl,
                    method: 'GET'
                })
            });

            const data = await response.json();
            this.displayProxyResponse(data);

        } catch (error) {
            console.error('خطأ في الطلب:', error);
            this.showNotification('فشل في تحميل الصفحة', 'error');
        }
    }

    displayProxyResponse(response) {
        const browserFrame = document.getElementById('browserFrame');
        
        if (response.success) {
            const statusClass = response.data.status < 400 ? 'success' : 'error';
            
            browserFrame.innerHTML = `
                <div class="response-content">
                    <div class="response-header">
                        <strong>${response.data.url}</strong>
                        <span class="response-status ${statusClass}">
                            ${response.data.status} ${response.data.statusText}
                        </span>
                    </div>
                    <div class="response-body">
                        ${this.formatResponseData(response.data.data)}
                    </div>
                </div>
            `;
            
            this.showNotification('تم تحميل الصفحة بنجاح', 'success');
        } else {
            browserFrame.innerHTML = `
                <div class="response-content">
                    <div class="response-header">
                        <strong>خطأ في التحميل</strong>
                        <span class="response-status error">ERROR</span>
                    </div>
                    <div class="response-body">
                        ${response.error}
                    </div>
                </div>
            `;
            
            this.showNotification('فشل في تحميل الصفحة', 'error');
        }
    }

    formatResponseData(data) {
        if (typeof data === 'string') {
            // إذا كانت البيانات HTML، عرضها كما هي
            if (data.includes('<html') || data.includes('<!DOCTYPE')) {
                return this.escapeHtml(data);
            }
            return this.escapeHtml(data);
        } else if (typeof data === 'object') {
            return this.escapeHtml(JSON.stringify(data, null, 2));
        }
        return this.escapeHtml(String(data));
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showNotification(message, type = 'info') {
        const notifications = document.getElementById('notifications');
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 1.2rem; cursor: pointer;">&times;</button>
            </div>
        `;

        notifications.appendChild(notification);

        // إزالة الإشعار تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// بدء تشغيل العميل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 بدء تشغيل VPN USA Simple Client');
    new SimpleVPNClient();
});
