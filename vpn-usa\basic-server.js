const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3001;

// خوادم أمريكية وهمية
const USA_SERVERS = [
  { id: 'us-east-1', name: 'New York', ip: '*************', port: 8080, load: 25 },
  { id: 'us-west-1', name: 'Los Angeles', ip: '*************', port: 8080, load: 40 },
  { id: 'us-central-1', name: 'Chicago', ip: '*************', port: 8080, load: 15 },
  { id: 'us-south-1', name: 'Miami', ip: '*************', port: 8080, load: 30 }
];

function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const types = {
    '.html': 'text/html; charset=utf-8',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json'
  };
  return types[ext] || 'text/plain';
}

const server = http.createServer((req, res) => {
  const url = new URL(req.url, `http://localhost:${PORT}`);
  const pathname = url.pathname;

  console.log(`${req.method} ${pathname}`);

  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // الصفحة الرئيسية
  if (pathname === '/' || pathname === '/index.html') {
    const filePath = path.join(__dirname, 'client', 'index.html');
    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(404);
        res.end('File not found');
        return;
      }
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(data);
    });
    return;
  }

  // الملفات الثابتة
  if (pathname.endsWith('.css') || pathname.endsWith('.js')) {
    const filePath = path.join(__dirname, 'client', pathname.substring(1));
    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(404);
        res.end('File not found');
        return;
      }
      res.writeHead(200, { 'Content-Type': getContentType(filePath) });
      res.end(data);
    });
    return;
  }

  // API للخوادم
  if (pathname === '/api/servers') {
    const serversWithStatus = USA_SERVERS.map(server => ({
      ...server,
      status: 'online',
      ping: Math.floor(Math.random() * 50) + 10
    }));

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      servers: serversWithStatus
    }));
    return;
  }

  // API للحالة
  if (pathname === '/api/status') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      connectedClients: Math.floor(Math.random() * 10) + 1,
      activeServers: USA_SERVERS.length,
      serverLoad: Math.floor(Math.random() * 60) + 20,
      uptime: process.uptime()
    }));
    return;
  }

  // API للـ IP
  if (pathname === '/api/ip') {
    const clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      ip: clientIp || '127.0.0.1',
      country: 'Local',
      city: 'Local'
    }));
    return;
  }

  // API للبروكسي
  if (pathname === '/api/proxy' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const requestData = JSON.parse(body);
        const targetUrl = requestData.url;
        
        // محاكاة استجابة البروكسي
        const mockResponse = {
          success: true,
          data: {
            status: 200,
            statusText: 'OK',
            headers: { 'content-type': 'text/html' },
            data: `<html><head><title>VPN USA Proxy</title></head><body><h1>تم تحميل الصفحة عبر VPN USA</h1><p>الرابط: ${targetUrl}</p><p>تم التحميل بنجاح عبر الخادم الأمريكي</p></body></html>`,
            url: targetUrl,
            method: 'GET',
            timestamp: new Date().toISOString()
          }
        };

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(mockResponse));

      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          error: 'Invalid request'
        }));
      }
    });
    return;
  }

  // 404
  res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
  res.end('<h1>404 - الصفحة غير موجودة</h1>');
});

server.listen(PORT, () => {
  console.log('🇺🇸 VPN USA Server Starting...');
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`🇺🇸 Available USA servers: ${USA_SERVERS.length}`);
});

server.on('error', (error) => {
  console.error('Server error:', error);
});
