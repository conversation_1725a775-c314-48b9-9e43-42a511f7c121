const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const ClientManager = require('./utils/client-manager');
const Encryption = require('./utils/encryption');
const ProxyHandler = require('./utils/proxy-handler');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// إعدادات الأمان
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'client')));

// إعدادات الخادم
const PORT = process.env.PORT || 3001;
const VPN_KEY = process.env.VPN_KEY || crypto.randomBytes(32).toString('hex');

// خوادم أمريكية وهمية (في التطبيق الحقيقي ستكون خوادم فعلية)
const USA_SERVERS = [
  { id: 'us-east-1', name: 'New York', ip: '*************', port: 8080, load: 25 },
  { id: 'us-west-1', name: 'Los Angeles', ip: '*************', port: 8080, load: 40 },
  { id: 'us-central-1', name: 'Chicago', ip: '*************', port: 8080, load: 15 },
  { id: 'us-south-1', name: 'Miami', ip: '*************', port: 8080, load: 30 }
];

// إنشاء مدير العملاء ونظام التشفير
const clientManager = new ClientManager();
const encryption = new Encryption(VPN_KEY);
const proxyHandler = new ProxyHandler();

console.log('🇺🇸 VPN USA Server Starting...');
console.log('🔐 VPN Key:', VPN_KEY.substring(0, 8) + '...');

// الصفحة الرئيسية
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'client', 'index.html'));
});

// API للحصول على قائمة الخوادم الأمريكية
app.get('/api/servers', (req, res) => {
  res.json({
    success: true,
    servers: USA_SERVERS.map(server => ({
      ...server,
      status: 'online',
      ping: Math.floor(Math.random() * 50) + 10 // محاكاة ping
    }))
  });
});

// API للحصول على معلومات الاتصال
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    connectedClients: clientManager.getClientCount(),
    activeServers: USA_SERVERS.length,
    serverLoad: Math.floor(Math.random() * 60) + 20,
    uptime: process.uptime()
  });
});

// API للحصول على IP الحالي
app.get('/api/ip', async (req, res) => {
  try {
    const clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    res.json({
      success: true,
      ip: clientIp,
      country: 'Unknown',
      city: 'Unknown'
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// معالجة اتصالات WebSocket
io.on('connection', (socket) => {
  console.log('🔌 عميل جديد متصل:', socket.id);
  
  // تسجيل العميل
  const clientId = uuidv4();
  const client = clientManager.addClient(clientId, socket.id, socket.handshake.address);
  
  socket.emit('connected', {
    clientId: clientId,
    message: 'مرحباً بك في VPN USA الخاص',
    availableServers: USA_SERVERS
  });

  // طلب الاتصال بخادم أمريكي
  socket.on('connect-to-server', (data) => {
    const { serverId } = data;
    const server = USA_SERVERS.find(s => s.id === serverId);
    
    if (server) {
      // محاكاة الاتصال بالخادم
      clientManager.updateClientServer(clientId, serverId);
      
      socket.emit('server-connected', {
        success: true,
        server: server,
        newIp: server.ip,
        message: `متصل بنجاح مع خادم ${server.name}`
      });
      
      console.log(`✅ العميل ${clientId} متصل مع ${server.name}`);
    } else {
      socket.emit('connection-error', {
        success: false,
        message: 'خادم غير موجود'
      });
    }
  });

  // قطع الاتصال مع الخادم
  socket.on('disconnect-server', () => {
    clientManager.updateClientServer(clientId, null);
    socket.emit('server-disconnected', {
      success: true,
      message: 'تم قطع الاتصال مع الخادم'
    });
    console.log(`❌ العميل ${clientId} قطع الاتصال`);
  });

  // معالجة طلبات البروكسي
  socket.on('proxy-request', async (data) => {
    try {
      const { url, method = 'GET', headers = {} } = data;
      const client = clientManager.getClient(clientId);
      
      if (client && client.connectedServer) {
        // تشفير الطلب
        const encryptedData = encryption.encrypt(JSON.stringify(data));
        
        // محاكاة إرسال الطلب عبر الخادم الأمريكي
        const response = await proxyHandler.handleRequest(url, method, headers);
        
        socket.emit('proxy-response', {
          success: true,
          data: response,
          server: client.connectedServer
        });
      } else {
        socket.emit('proxy-response', {
          success: false,
          error: 'غير متصل بأي خادم'
        });
      }
    } catch (error) {
      socket.emit('proxy-response', {
        success: false,
        error: error.message
      });
    }
  });

  // عند قطع الاتصال
  socket.on('disconnect', () => {
    clientManager.removeClient(clientId);
    console.log('🔌 عميل منقطع:', socket.id);
  });
});

// بدء الخادم
server.listen(PORT, () => {
  console.log(`🚀 VPN USA Server running on port ${PORT}`);
  console.log(`🌐 Access dashboard: http://localhost:${PORT}`);
  console.log(`🇺🇸 Available USA servers: ${USA_SERVERS.length}`);
});

// معالجة الأخطاء
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});
